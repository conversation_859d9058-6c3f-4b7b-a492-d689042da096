import { useState, useEffect } from "react";
import { RefreshCw, Clock, Users, Thermometer, Waves, TrendingUp, Calendar } from "lucide-react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";

import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from "recharts";
import StatusBadge from "@/components/StatusBadge";
import { cn } from "@/lib/utils";
import poolData from "@/mockData/pool.json";
import poolUsualLoad from "@/mockData/poolUsualLoad.json";

const getCapacityStatus = (occupancy: number) => {
  if (occupancy >= 0.9) return "full";
  if (occupancy >= 0.7) return "high";
  if (occupancy >= 0.4) return "moderate";
  return "low";
};

const Pool = () => {
  const [pool, setPool] = useState(poolData.data);
  const [usualLoad] = useState(poolUsualLoad.data);
  const [lastUpdated, setLastUpdated] = useState(new Date());
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [selectedDay, setSelectedDay] = useState(() => {
    const today = new Date().toLocaleDateString('cs-CZ', { weekday: 'long' });
    return today;
  });

  // Check if today is the current day to show real-time overlay
  const isCurrentDay = () => {
    const today = new Date().toLocaleDateString('cs-CZ', { weekday: 'long' });
    return today === selectedDay;
  };

  // Prepare chart data with current day overlay if applicable
  const getChartData = () => {
    const usualData = usualLoad.days?.[selectedDay] || [];

    if (!isCurrentDay() || !usualLoad.current_day_data?.enabled) {
      return usualData;
    }

    // Find current day data
    const currentData = usualLoad.current_day_data.current_time_data[0];

    if (!currentData) {
      return usualData;
    }

    // Add current data point to the usual data
    const currentTime = currentData.time;
    const enhancedData = usualData.map(point => {
      if (point.time === currentTime) {
        return {
          ...point,
          current_occupancy: currentData.current_occupancy,
          current_visitors: currentData.current_visitors
        };
      }
      return point;
    });

    // If current time doesn't exist in usual data, add it
    const timeExists = usualData.some(point => point.time === currentTime);
    if (!timeExists) {
      enhancedData.push({
        time: currentTime,
        usual_occupancy: 0, // We don't have usual data for this exact time
        usual_visitors: 0,
        current_occupancy: currentData.current_occupancy,
        current_visitors: currentData.current_visitors
      });
      // Sort by time
      enhancedData.sort((a, b) => a.time.localeCompare(b.time));
    }

    return enhancedData;
  };

  const refreshData = async () => {
    setIsRefreshing(true);
    await new Promise(resolve => setTimeout(resolve, 1000));
    setLastUpdated(new Date());
    setIsRefreshing(false);
  };

  useEffect(() => {
    const interval = setInterval(refreshData, 300000); // 5 minutes
    return () => clearInterval(interval);
  }, []);

  const occupancyPercent = Math.round(pool.occupancy_percentage * 100);
  const availableSpots = pool.total_capacity - pool.current_visitors;

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h1 className="text-3xl font-bold text-foreground mb-2">
              Městský bazén České Budějovice
            </h1>
            <p className="text-muted-foreground">
              Aktuální obsazenost a informace o provozu
            </p>
          </div>
          <Button
            onClick={refreshData}
            disabled={isRefreshing}
            variant="outline"
            size="sm"
            className="flex items-center gap-2"
          >
            <RefreshCw className={cn("h-4 w-4", isRefreshing && "animate-spin")} />
            Obnovit
          </Button>
        </div>
        
        <div className="flex items-center gap-2 text-sm text-muted-foreground">
          <Clock className="h-4 w-4" />
          Poslední aktualizace: {lastUpdated.toLocaleTimeString("cs-CZ")}
        </div>
      </div>

      {/* Main Capacity Card - Fixed Layout */}
      <Card className="mb-8 shadow-civic">
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="p-3 bg-gradient-primary rounded-lg">
                <Waves className="h-8 w-8 text-primary-foreground" />
              </div>
              <div>
                <CardTitle className="text-2xl mb-1">Aktuální obsazenost</CardTitle>
                <StatusBadge status={pool.is_open ? getCapacityStatus(pool.occupancy_percentage) : "closed"}>
                  {pool.is_open ? `${occupancyPercent}% obsazeno` : "Zavřeno"}
                </StatusBadge>
              </div>
            </div>
            <div className="text-right">
              <div className="text-6xl font-bold text-primary mb-1">
                {pool.current_visitors}
              </div>
              <div className="text-sm text-muted-foreground">
                z {pool.total_capacity} míst
              </div>
            </div>
          </div>
        </CardHeader>
        
        <CardContent>
          {/* Capacity Progress Bar */}
          <div className="mb-6">
            <div className="flex justify-between items-center mb-3">
              <span className="text-sm font-medium">Kapacita bazénu</span>
              <span className="text-sm text-muted-foreground">
                Volné místa: {availableSpots}
              </span>
            </div>
            <div className="w-full bg-muted rounded-full h-4">
              <div
                className={cn(
                  "h-4 rounded-full transition-smooth",
                  pool.occupancy_percentage >= 0.9 ? "bg-destructive" :
                  pool.occupancy_percentage >= 0.7 ? "bg-warning" :
                  pool.occupancy_percentage >= 0.4 ? "bg-secondary" :
                  "bg-success"
                )}
                style={{ width: `${occupancyPercent}%` }}
              />
            </div>
          </div>

          {/* Fixed Quick Stats */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center p-4 bg-muted/50 rounded-lg">
              <Users className="h-6 w-6 mx-auto mb-2 text-primary" />
              <div className="text-2xl font-bold text-foreground">{pool.current_visitors}</div>
              <div className="text-sm text-muted-foreground">Návštěvníci</div>
            </div>
            <div className="text-center p-4 bg-muted/50 rounded-lg">
              <Thermometer className="h-6 w-6 mx-auto mb-2 text-secondary" />
              <div className="text-2xl font-bold text-foreground">{pool.water_temperature}°C</div>
              <div className="text-sm text-muted-foreground">Teplota vody</div>
            </div>
            <div className="text-center p-4 bg-muted/50 rounded-lg">
              <div className="text-2xl font-bold text-foreground">{occupancyPercent}%</div>
              <div className="text-sm text-muted-foreground">Obsazenost</div>
            </div>
            <div className="text-center p-4 bg-muted/50 rounded-lg">
              <div className="text-2xl font-bold text-success">{availableSpots}</div>
              <div className="text-sm text-muted-foreground">Volných míst</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Usual Load Data */}
      <Card className="mb-8 shadow-civic">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TrendingUp className="h-5 w-5" />
            Obvyklé vytížení
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="mb-4">
            <Select value={selectedDay} onValueChange={setSelectedDay}>
              <SelectTrigger className="w-48">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {Object.keys(usualLoad.days).map((day) => (
                  <SelectItem key={day} value={day}>
                    {day.charAt(0).toUpperCase() + day.slice(1)}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="h-80 w-full">
            <ResponsiveContainer width="100%" height="100%">
              <LineChart data={getChartData()}>
                <CartesianGrid strokeDasharray="3 3" className="opacity-30" />
                <XAxis
                  dataKey="time"
                  tick={{ fontSize: 12 }}
                  tickLine={{ stroke: 'hsl(var(--muted-foreground))' }}
                />
                <YAxis
                  tick={{ fontSize: 12 }}
                  tickLine={{ stroke: 'hsl(var(--muted-foreground))' }}
                  domain={[0, 1]}
                  tickFormatter={(value) => `${Math.round(value * 100)}%`}
                />
                <Tooltip
                  formatter={(value: number, name) => [
                    name === 'usual_occupancy' ? `${Math.round(value * 100)}%` :
                    name === 'current_occupancy' ? `${Math.round(value * 100)}%` : value,
                    name === 'usual_occupancy' ? 'Obvyklá obsazenost' :
                    name === 'current_occupancy' ? 'Aktuální obsazenost' : 'Návštěvníci'
                  ]}
                  labelFormatter={(label) => `Čas: ${label}`}
                  contentStyle={{
                    backgroundColor: 'hsl(var(--card))',
                    border: '1px solid hsl(var(--border))',
                    borderRadius: '8px'
                  }}
                />
                <Line
                  type="monotone"
                  dataKey="usual_occupancy"
                  stroke="hsl(var(--primary))"
                  strokeWidth={3}
                  dot={{ r: 4, fill: 'hsl(var(--primary))' }}
                  activeDot={{ r: 6, fill: 'hsl(var(--primary))' }}
                  name="usual_occupancy"
                />
                {isCurrentDay() && usualLoad.current_day_data?.enabled && (
                  <Line
                    type="monotone"
                    dataKey="current_occupancy"
                    stroke="hsl(var(--destructive))"
                    strokeWidth={3}
                    strokeDasharray="5 5"
                    dot={{ r: 4, fill: 'hsl(var(--destructive))' }}
                    activeDot={{ r: 6, fill: 'hsl(var(--destructive))' }}
                    name="current_occupancy"
                  />
                )}
              </LineChart>
            </ResponsiveContainer>
          </div>

          <div className="mt-4 p-4 bg-muted/50 rounded-lg">
            <h4 className="font-semibold mb-2">Nejrušnější doba:</h4>
            <p className="text-sm text-muted-foreground">
              14:00-16:00 (průměrně 70-75% obsazenost)
            </p>
            {isCurrentDay() && (
              <div className="mt-4 p-3 bg-info-light rounded-lg border border-info/20">
                <h5 className="font-medium text-info mb-1">Aktuální den</h5>
                <p className="text-sm text-info/80">
                  Červená čárkovaná čára zobrazuje aktuální obsazenost pro dnešní den ve srovnání s obvyklými hodnotami.
                </p>
              </div>
            )}
          </div>

          {/* Best Visit Times */}
          <div className="mt-4 space-y-3">
            <h4 className="font-semibold">Doporučené časy návštěvy:</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              {usualLoad.best_visit_times.map((time, index) => (
                <div key={index} className="p-3 bg-success-light rounded-lg border border-success/20">
                  <div className="flex items-center gap-2 mb-1">
                    <Calendar className="h-4 w-4 text-success" />
                    <span className="font-medium text-success">{time.time}</span>
                  </div>
                  <p className="text-sm text-success/80">{time.reason}</p>
                </div>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Facilities Info */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <Card className="shadow-civic">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Waves className="h-5 w-5 text-primary" />
              Hlavní bazén
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-sm text-muted-foreground">Délka:</span>
                <span className="font-medium">{pool.facilities.main_pool.length}m</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-muted-foreground">Dráhy:</span>
                <span className="font-medium">{pool.facilities.main_pool.lanes}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-muted-foreground">Teplota:</span>
                <span className="font-medium">{pool.facilities.main_pool.temperature}°C</span>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="shadow-civic">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Users className="h-5 w-5 text-secondary" />
              Dětský bazén
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-sm text-muted-foreground">Hloubka:</span>
                <span className="font-medium">{pool.facilities.kids_pool.depth}m</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-muted-foreground">Teplota:</span>
                <span className="font-medium">{pool.facilities.kids_pool.temperature}°C</span>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="shadow-civic">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Thermometer className="h-5 w-5 text-warning" />
              Sauna
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-sm text-muted-foreground">Dostupnost:</span>
                <StatusBadge status={pool.facilities.sauna.available ? "low" : "closed"}>
                  {pool.facilities.sauna.available ? "Dostupná" : "Nedostupná"}
                </StatusBadge>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-muted-foreground">Teplota:</span>
                <span className="font-medium">{pool.facilities.sauna.temperature}°C</span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Capacity Legend */}
      <div className="p-6 bg-muted/50 rounded-lg">
        <h3 className="text-lg font-semibold mb-4">Vysvětlivky obsazenosti</h3>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="flex items-center gap-2">
            <StatusBadge status="low">Nízká</StatusBadge>
            <span className="text-sm">{'<'} 40% obsazeno</span>
          </div>
          <div className="flex items-center gap-2">
            <StatusBadge status="moderate">Střední</StatusBadge>
            <span className="text-sm">40-70% obsazeno</span>
          </div>
          <div className="flex items-center gap-2">
            <StatusBadge status="high">Vysoká</StatusBadge>
            <span className="text-sm">70-90% obsazeno</span>
          </div>
          <div className="flex items-center gap-2">
            <StatusBadge status="full">Plná</StatusBadge>
            <span className="text-sm">{'>'} 90% obsazeno</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Pool;