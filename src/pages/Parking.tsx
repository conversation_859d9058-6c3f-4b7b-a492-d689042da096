import { useEffect, useState } from "react";
import { <PERSON>f<PERSON><PERSON><PERSON>, MapPin, Clock, Car, TrendingUp, Calendar } from "lucide-react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";

import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from "recharts";
import StatusBadge from "@/components/StatusBadge";
import { cn } from "@/lib/utils";
import parkingData from "@/mockData/parking.json";
import parkingUsualLoad from "@/mockData/parkingUsualLoad.json";

interface ParkingSpot {
  name: string;
  location: string;
  capacity: number;
  available: number;
  occupancy: number;
  last_updated: string;
  type: string;
}

const getOccupancyStatus = (occupancy: number) => {
  if (occupancy >= 0.9) return "full";
  if (occupancy >= 0.6) return "high";
  if (occupancy >= 0.3) return "moderate";
  return "low";
};

const getOccupancyColor = (occupancy: number) => {
  if (occupancy >= 0.9) return "text-destructive";
  if (occupancy >= 0.6) return "text-warning";
  if (occupancy >= 0.3) return "text-warning";
  return "text-success";
};

const Parking = () => {
  const [parking, setParking] = useState<ParkingSpot[]>(parkingData.data);
  const [usualLoad] = useState(parkingUsualLoad.data);
  const [selectedLocation, setSelectedLocation] = useState("Parkoviště Senovážné nám.");
  const [selectedDay, setSelectedDay] = useState(() => {
    const days = ['neděle', 'pondělí', 'úterý', 'středa', 'čtvrtek', 'pátek', 'sobota'];
    return days[new Date().getDay()];
  });
  const [lastUpdated, setLastUpdated] = useState(new Date());
  const [isRefreshing, setIsRefreshing] = useState(false);

  // Check if today is the current day to show real-time overlay
  const isCurrentDay = () => {
    const today = new Date();
    const days = ['neděle', 'pondělí', 'úterý', 'středa', 'čtvrtek', 'pátek', 'sobota'];
    return days[today.getDay()] === selectedDay;
  };

  // Prepare chart data with current day overlay if applicable
  const getChartData = () => {
    const usualData = usualLoad.locations[selectedLocation]?.days?.[selectedDay] || [];

    if (!isCurrentDay() || !usualLoad.current_day_data?.enabled) {
      return usualData;
    }

    // Find current day data for the selected location
    const currentData = usualLoad.current_day_data.current_time_data.find(
      item => item.location === selectedLocation
    );

    if (!currentData) {
      return usualData;
    }

    // Add current data point to the usual data
    const currentTime = currentData.time;
    const enhancedData = usualData.map(point => {
      if (point.time === currentTime) {
        return {
          ...point,
          current_occupancy: currentData.current_occupancy,
          current_available: currentData.current_available
        };
      }
      return point;
    });

    // If current time doesn't exist in usual data, add it
    const timeExists = usualData.some(point => point.time === currentTime);
    if (!timeExists) {
      enhancedData.push({
        time: currentTime,
        usual_occupancy: 0, // We don't have usual data for this exact time
        usual_available: 0,
        current_occupancy: currentData.current_occupancy,
        current_available: currentData.current_available
      });
      // Sort by time
      enhancedData.sort((a, b) => a.time.localeCompare(b.time));
    }

    return enhancedData;
  };

  const refreshData = async () => {
    setIsRefreshing(true);
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000));
    setLastUpdated(new Date());
    setIsRefreshing(false);
  };

  useEffect(() => {
    // Auto-refresh every 2 minutes
    const interval = setInterval(refreshData, 120000);
    return () => clearInterval(interval);
  }, []);

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h1 className="text-3xl font-bold text-foreground mb-2">
              Parkování v České Budějovice
            </h1>
            <p className="text-muted-foreground">
              Aktuální obsazenost parkovišť v reálném čase
            </p>
          </div>
          <Button
            onClick={refreshData}
            disabled={isRefreshing}
            variant="outline"
            size="sm"
            className="flex items-center gap-2"
          >
            <RefreshCw className={cn("h-4 w-4", isRefreshing && "animate-spin")} />
            Obnovit
          </Button>
        </div>
        
        <div className="flex items-center gap-2 text-sm text-muted-foreground">
          <Clock className="h-4 w-4" />
          Poslední aktualizace: {lastUpdated.toLocaleTimeString("cs-CZ")}
        </div>
      </div>

      {/* Parking Cards Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
        {parking.map((spot, index) => (
          <Card key={index} className="relative overflow-hidden shadow-civic hover:shadow-lg transition-smooth">
            <CardHeader className="pb-3">
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <CardTitle className="text-lg font-semibold mb-1">
                    {spot.name}
                  </CardTitle>
                  <div className="flex items-center text-sm text-muted-foreground">
                    <MapPin className="h-4 w-4 mr-1" />
                    {spot.location}
                  </div>
                </div>
                <StatusBadge status={getOccupancyStatus(spot.occupancy)}>
                  {Math.round((1 - spot.occupancy) * 100)}% volné
                </StatusBadge>
              </div>
            </CardHeader>
            
            <CardContent>
              {/* Occupancy Bar */}
              <div className="mb-4">
                <div className="flex justify-between items-center mb-2">
                  <span className="text-sm text-muted-foreground">Obsazenost</span>
                  <span className={cn("text-sm font-medium", getOccupancyColor(spot.occupancy))}>
                    {Math.round(spot.occupancy * 100)}%
                  </span>
                </div>
                <div className="w-full bg-muted rounded-full h-2">
                  <div
                    className={cn(
                      "h-2 rounded-full transition-smooth",
                      spot.occupancy >= 0.9 ? "bg-destructive" :
                      spot.occupancy >= 0.6 ? "bg-warning" :
                      spot.occupancy >= 0.3 ? "bg-warning" :
                      "bg-success"
                    )}
                    style={{ width: `${spot.occupancy * 100}%` }}
                  />
                </div>
              </div>

              {/* Stats */}
              <div className="space-y-2">
                <div className="flex justify-between items-center text-sm">
                  <span className="text-muted-foreground">Volná místa:</span>
                  <span className="font-medium text-success">{spot.available}</span>
                </div>
                <div className="flex justify-between items-center text-sm">
                  <span className="text-muted-foreground">Celková kapacita:</span>
                  <span className="font-medium">{spot.capacity}</span>
                </div>
                <div className="flex justify-between items-center text-sm">
                  <span className="text-muted-foreground">Typ parkoviště:</span>
                  <span className="font-medium">
                    {spot.type === "street" ? "Ulice" : "Garáž"}
                  </span>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Usual Load Data */}
      <Card className="mb-8 shadow-civic">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TrendingUp className="h-5 w-5" />
            Obvyklé vytížení
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="mb-4 flex flex-col md:flex-row gap-4">
            <Select value={selectedLocation} onValueChange={setSelectedLocation}>
              <SelectTrigger className="w-full md:w-80">
                <SelectValue placeholder="Vyberte parkoviště" />
              </SelectTrigger>
              <SelectContent>
                {Object.keys(usualLoad.locations).map((location) => (
                  <SelectItem key={location} value={location}>
                    {location}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Select value={selectedDay} onValueChange={setSelectedDay}>
              <SelectTrigger className="w-full md:w-48">
                <SelectValue placeholder="Vyberte den" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="pondělí">Pondělí</SelectItem>
                <SelectItem value="úterý">Úterý</SelectItem>
                <SelectItem value="středa">Středa</SelectItem>
                <SelectItem value="čtvrtek">Čtvrtek</SelectItem>
                <SelectItem value="pátek">Pátek</SelectItem>
                <SelectItem value="sobota">Sobota</SelectItem>
                <SelectItem value="neděle">Neděle</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="h-80 w-full">
            <ResponsiveContainer width="100%" height="100%">
              <LineChart data={getChartData()}>
                <CartesianGrid strokeDasharray="3 3" className="opacity-30" />
                <XAxis
                  dataKey="time"
                  tick={{ fontSize: 12 }}
                  tickLine={{ stroke: 'hsl(var(--muted-foreground))' }}
                />
                <YAxis
                  tick={{ fontSize: 12 }}
                  tickLine={{ stroke: 'hsl(var(--muted-foreground))' }}
                  domain={[0, 1]}
                  tickFormatter={(value) => `${Math.round(value * 100)}%`}
                />
                <Tooltip
                  formatter={(value: number, name) => [
                    name === 'usual_occupancy' ? `${Math.round(value * 100)}%` :
                    name === 'current_occupancy' ? `${Math.round(value * 100)}%` : `${value} míst`,
                    name === 'usual_occupancy' ? 'Obvyklá obsazenost' :
                    name === 'current_occupancy' ? 'Aktuální obsazenost' : 'Volná místa'
                  ]}
                  labelFormatter={(label) => `Čas: ${label}`}
                  contentStyle={{
                    backgroundColor: 'hsl(var(--card))',
                    border: '1px solid hsl(var(--border))',
                    borderRadius: '8px'
                  }}
                />
                <Line
                  type="monotone"
                  dataKey="usual_occupancy"
                  stroke="hsl(var(--primary))"
                  strokeWidth={3}
                  dot={{ r: 4, fill: 'hsl(var(--primary))' }}
                  activeDot={{ r: 6, fill: 'hsl(var(--primary))' }}
                  name="usual_occupancy"
                />
                {isCurrentDay() && usualLoad.current_day_data?.enabled && (
                  <Line
                    type="monotone"
                    dataKey="current_occupancy"
                    stroke="hsl(var(--destructive))"
                    strokeWidth={3}
                    strokeDasharray="5 5"
                    dot={{ r: 4, fill: 'hsl(var(--destructive))' }}
                    activeDot={{ r: 6, fill: 'hsl(var(--destructive))' }}
                    name="current_occupancy"
                  />
                )}
              </LineChart>
            </ResponsiveContainer>
          </div>

          <div className="mt-4 p-4 bg-muted/50 rounded-lg">
            <h4 className="font-semibold mb-2">Týdenní vzorce:</h4>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
              <div>
                <span className="font-medium">Nejrušnější dny:</span>
                <p className="text-muted-foreground">Pátek, Sobota</p>
              </div>
              <div>
                <span className="font-medium">Špičkové hodiny:</span>
                <p className="text-muted-foreground">09:00-11:00, 14:00-16:00</p>
              </div>
              <div>
                <span className="font-medium">Nejklidnější:</span>
                <p className="text-muted-foreground">Neděle ráno</p>
              </div>
            </div>
            {isCurrentDay() && (
              <div className="mt-4 p-3 bg-info-light rounded-lg border border-info/20">
                <h5 className="font-medium text-info mb-1">Aktuální den</h5>
                <p className="text-sm text-info/80">
                  Červená čárkovaná čára zobrazuje aktuální obsazenost pro dnešní den ve srovnání s obvyklými hodnotami.
                </p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Legend */}
      <div className="mt-8 p-6 bg-muted/50 rounded-lg">
        <h3 className="text-lg font-semibold mb-4">Vysvětlivky</h3>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="flex items-center gap-2">
            <StatusBadge status="low">Nízké</StatusBadge>
            <span className="text-sm">{'<'} 30% obsazeno</span>
          </div>
          <div className="flex items-center gap-2">
            <StatusBadge status="moderate">Střední</StatusBadge>
            <span className="text-sm">30-60% obsazeno</span>
          </div>
          <div className="flex items-center gap-2">
            <StatusBadge status="high">Vysoké</StatusBadge>
            <span className="text-sm">60-90% obsazeno</span>
          </div>
          <div className="flex items-center gap-2">
            <StatusBadge status="full">Plné</StatusBadge>
            <span className="text-sm">{'>'} 90% obsazeno</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Parking;